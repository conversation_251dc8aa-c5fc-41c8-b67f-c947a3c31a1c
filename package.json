{"name": "sela-web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "dev:all": "concurrently \"bun run dev\" \"cd server && bun run dev\"", "type-check": "tsc --noEmit"}, "dependencies": {"@headlessui/react": "^2.1.2", "@heroicons/react": "^2.1.5", "@hookform/resolvers": "^3.9.0", "@supabase/supabase-js": "^2.45.1", "@tanstack/react-query": "^5.51.23", "clsx": "^2.1.1", "date-fns": "^3.6.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.52.1", "react-player": "^2.16.0", "react-router-dom": "^6.26.0", "tailwind-merge": "^2.4.0", "zod": "^3.23.8", "zustand": "^4.5.4"}, "devDependencies": {"@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@typescript-eslint/eslint-plugin": "^8.37.0", "@typescript-eslint/parser": "^8.37.0", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.19", "concurrently": "^8.2.2", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.7", "postcss": "^8.4.40", "tailwindcss": "^3.4.7", "terser": "^5.43.1", "typescript": "^5.2.2", "vite": "^7.0.4"}}