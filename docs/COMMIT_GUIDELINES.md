# Quy Tắc Commit Guidelines

## 📋 Tổng Quan

Tài liệu này định nghĩa các quy tắc và tiêu chuẩn cho việc viết commit messages trong dự án SELA_Web.

## 🎯 Cấu Trúc Commit Message

### Format Chuẩn

```
<type>(<scope>): <subject>

<body>

<footer>
```

### Ví <PERSON>

```
feat(auth): add user login functionality

- Implement JWT token authentication
- Add login form validation
- Create user session management

Closes #123
```

## 📝 Các Loại Commit (Type)

| Type       | Mô Tả                                           | Ví Dụ                                            |
| ---------- | ----------------------------------------------- | ------------------------------------------------ |
| `feat`     | Tính năng mới                                   | `feat(courses): add course enrollment`           |
| `fix`      | Sửa lỗi                                         | `fix(auth): resolve login validation error`      |
| `docs`     | Cập nhật tài liệu                               | `docs(readme): update installation guide`        |
| `style`    | Thay đổi formatting, không ảnh hưởng code       | `style(components): fix indentation`             |
| `refactor` | Refactor code, không thêm tính năng hay fix bug | `refactor(utils): simplify validation functions` |
| `test`     | Thêm hoặc sửa tests                             | `test(auth): add unit tests for login service`   |
| `chore`    | Thay đổi build process, dependencies            | `chore(deps): update React to v18`               |
| `perf`     | Cải thiện performance                           | `perf(components): optimize course rendering`    |
| `ci`       | Thay đổi CI/CD                                  | `ci(github): add automated testing workflow`     |
| `build`    | Thay đổi build system                           | `build(vite): update build configuration`        |
| `revert`   | Revert commit trước đó                          | `revert(auth): revert login changes`             |

## 🎯 Scope (Phạm Vi)

### Các Scope Thường Dùng

- `auth` - Authentication/Authorization
- `courses` - Course management
- `progress` - Progress tracking
- `components` - UI Components
- `services` - API services
- `utils` - Utility functions
- `types` - TypeScript types
- `config` - Configuration
- `styles` - Styling/CSS
- `tests` - Testing

### Ví Dụ Scope

```
feat(auth): add password reset functionality
fix(courses): resolve course loading issue
docs(api): update endpoint documentation
```

## ✅ Quy Tắc Subject

### Làm Gì:

- ✅ Sử dụng thì hiện tại, dạng mệnh lệnh ("add", "fix", "update")
- ✅ Bắt đầu bằng chữ thường
- ✅ Không kết thúc bằng dấu chấm (.)
- ✅ Giới hạn 50 ký tự
- ✅ Mô tả ngắn gọn những gì commit làm

### Không Làm:

- ❌ Sử dụng thì quá khứ ("added", "fixed")
- ❌ Bắt đầu bằng chữ hoa
- ❌ Quá dài (>50 ký tự)
- ❌ Mô tả mơ hồ

### Ví Dụ Tốt:

```
feat(auth): add user registration form
fix(courses): resolve video player loading
docs(readme): update project setup guide
```

### Ví Dụ Không Tốt:

```
Added new feature for user registration
Fix bugs
Update stuff
Fixed the issue with video player not loading properly in course detail page
```

## 📖 Body (Nội Dung)

### Quy Tắc:

- Tùy chọn, nhưng khuyến khích cho các thay đổi phức tạp
- Giải thích **TẠI SAO** thay vì **GÌ**
- Ngắt dòng ở 72 ký tự
- Tách biệt với subject bằng dòng trống

### Ví Dụ:

```
feat(auth): add two-factor authentication

Users can now enable 2FA for enhanced security.
This feature uses TOTP (Time-based One-Time Password)
and integrates with popular authenticator apps.

The implementation includes:
- QR code generation for easy setup
- Backup codes for account recovery
- Admin panel for managing user 2FA status
```

## 🔗 Footer (Chân Trang)

### Mục Đích:

- Tham chiếu issues/tickets
- Breaking changes
- Metadata khác

### Ví Dụ:

```
feat(api): add new course API endpoints

BREAKING CHANGE: Course API now requires authentication
for all endpoints. Update client applications accordingly.

Closes #456
Refs #123, #789
```

## 🚀 Best Practices

### 1. Commit Thường Xuyên

- Commit các thay đổi nhỏ, tập trung
- Mỗi commit nên đại diện cho một thay đổi logic

### 2. Atomic Commits

- Mỗi commit nên hoàn chỉnh và có thể deploy độc lập
- Tránh mix nhiều tính năng trong một commit

### 3. Meaningful Messages

- Viết commit message như bạn đang giải thích cho đồng nghiệp
- Tưởng tượng bạn đang đọc lại sau 6 tháng

### 4. Consistency

- Luôn follow format đã định
- Sử dụng ngôn ngữ tiếng Anh cho commit messages

## 📊 Ví Dụ Commit Messages Tốt

```bash
# Tính năng mới
feat(courses): add course search functionality
feat(auth): implement OAuth integration with Google
feat(progress): add progress tracking dashboard

# Sửa lỗi
fix(auth): resolve token refresh issue
fix(courses): correct course enrollment validation
fix(ui): fix responsive layout on mobile devices

# Cải thiện
refactor(services): simplify API error handling
perf(components): optimize course list rendering
style(components): improve button hover animations

# Tài liệu
docs(api): add API documentation for course endpoints
docs(readme): update development setup instructions

# Bảo trì
chore(deps): update all dependencies to latest versions
ci(github): add automated deployment workflow
build(vite): optimize build performance
```

## 🚫 Ví Dụ Commit Messages Tệ

```bash
# Quá mơ hồ
fix: bug fixes
update: changes
improvement: better code

# Quá dài
feat: add a new feature that allows users to search for courses by various criteria including name, category, instructor, and difficulty level

# Không đúng format
Added new login page
Fixed some bugs in the authentication system
Updating the course management functionality

# Không có context
fix: undefined error
update: refactoring
change: modify files
```

## 🔧 Tools Hỗ Trợ

### Git Hooks

Có thể setup git hooks để tự động validate commit messages:

```bash
# .git/hooks/commit-msg
#!/bin/sh
commit_regex='^(feat|fix|docs|style|refactor|test|chore|perf|ci|build|revert)(\(.+\))?: .{1,50}'

if ! grep -qE "$commit_regex" "$1"; then
    echo "Invalid commit message format!"
    echo "Please use: <type>(<scope>): <subject>"
    exit 1
fi
```

### Commitizen

Cài đặt commitizen để hỗ trợ viết commit messages:

```bash
bun install -g commitizen
bun install -g cz-conventional-changelog
```

## 📚 Tham Khảo

- [Conventional Commits](https://www.conventionalcommits.org/)
- [Angular Commit Message Guidelines](https://github.com/angular/angular/blob/master/CONTRIBUTING.md#commit)
- [How to Write a Git Commit Message](https://chris.beams.io/posts/git-commit/)

## 📝 Checklist Trước Khi Commit

- [ ] Commit message follow đúng format `<type>(<scope>): <subject>`
- [ ] Type và scope phù hợp với thay đổi
- [ ] Subject ngắn gọn, rõ ràng (<50 ký tự)
- [ ] Body giải thích lý do thay đổi (nếu cần)
- [ ] Tham chiếu issues/tickets (nếu có)
- [ ] Code đã được test và review
- [ ] Không có debug code hay console.log
- [ ] Tất cả conflicts đã được resolve

---

_Tài liệu này sẽ được cập nhật thường xuyên để phù hợp với needs của team và project._
