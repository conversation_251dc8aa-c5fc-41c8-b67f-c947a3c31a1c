import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  server: {
    port: 3000,
    hmr: { overlay: false }, // Faster development
  },
  build: {
    rollupOptions: {
      // Windows-specific configuration for Rollup native binaries
      ...(process.platform === 'win32' && {
        onwarn(warning, warn) {
          // Suppress warnings about missing optional dependencies on Windows
          if (warning.code === 'UNRESOLVED_IMPORT' && warning.source?.includes('@rollup/rollup-win32')) {
            return;
          }
          warn(warning);
        },
      }),
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          router: ['react-router-dom'],
          ui: ['@headlessui/react'],
          utils: ['date-fns', 'zod'],
        },
      },
    },
    target: 'es2020', // Updated for React 19
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
      },
    },
  },
});
