# SELA_Web

A modern React-based web application built with Vite and powered by Bun.

## 🚀 Quick Start

### Prerequisites

- [Bun](https://bun.sh) v1.2.18 or higher
- **Windows Users**: See [Windows Setup Guide](./WINDOWS_SETUP.md) for platform-specific instructions

### Installation

1. Clone the repository:

```bash
git clone <repository-url>
cd SELA_Web
```

2. Install dependencies:

**For Windows users:**

```powershell
# Run the automated setup script (recommended)
.\setup-windows.ps1

# Or manual installation
$env:BUN_INSTALL_OPTIONAL = "true"
bun install
bun add --optional @rollup/rollup-win32-x64-msvc
```

**For macOS/Linux:**

```bash
bun install
```

### Development

Start the development server:

```bash
bun dev
```

The application will be available at `http://localhost:3000`

### Building for Production

Build the application:

```bash
bun run build
```

Preview the production build:

```bash
bun run preview
```

### Other Scripts

- `bun run lint` - Run ESLint
- `bun run type-check` - Run TypeScript type checking
- `bun run dev:all` - Start both frontend and backend development servers

## 🛠️ Tech Stack

- **Runtime & Package Manager**: Bun v1.2.18
- **Framework**: React 19.1.0
- **Build Tool**: Vite 7.0.4
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **State Management**: Zustand
- **Routing**: React Router
- **Forms**: React Hook Form + Zod
- **Backend**: Supabase

## 📁 Project Structure

```
src/
├── components/     # Reusable UI components
├── features/       # Feature-specific modules
├── layouts/        # Layout components
├── pages/          # Page components
├── services/       # API and external services
├── store/          # State management
├── types/          # TypeScript type definitions
└── utils/          # Utility functions
```

## 🔧 Migration to Bun

This project has been migrated from npm to Bun v1.2.18 for improved performance and developer experience. Key benefits include:

- **Faster installs**: Up to 25x faster than npm
- **Faster runtime**: Built-in bundler and transpiler
- **Better DX**: Unified toolchain for package management and runtime
- **Compatibility**: Drop-in replacement for npm/yarn

All existing functionality has been preserved during the migration.

## 🔧 Troubleshooting

### Windows Issues

If you encounter issues on Windows:

1. **Missing native.js file**: Run the Windows setup script or manually install Rollup binaries
2. **Blank white page**: Check browser console for errors and ensure all dependencies are installed
3. **Build failures**: Try using npm as a fallback: `npm install && npm run dev`

See [WINDOWS_SETUP.md](./WINDOWS_SETUP.md) for detailed Windows-specific instructions.

### General Issues

- **Port already in use**: Change the port in `vite.config.ts` or kill the process using port 3000
- **TypeScript errors**: Run `bun run type-check` to see detailed type errors
- **Build issues**: Clear `node_modules` and reinstall dependencies
