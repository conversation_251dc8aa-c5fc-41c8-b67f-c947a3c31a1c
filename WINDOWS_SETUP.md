# Windows Setup Guide for SELA_Web

This guide addresses Windows-specific issues when running SELA_Web with Bun v1.2.18.

## 🚨 Known Issues on Windows

### 1. Missing native.js File (Rollup Native Binaries)

**Problem**: Bun doesn't properly install optional dependencies for Windows platforms, causing Rollup to fail loading native binaries.

**Symptoms**:
- Error about missing "native.js" file
- Blank white page in browser
- Build failures or slow performance

### 2. Platform-Specific Dependencies

Bun has known issues with optional dependencies that are platform-specific, particularly affecting:
- `@rollup/rollup-win32-x64-msvc`
- `@rollup/rollup-win32-arm64-msvc`
- `@rollup/rollup-win32-ia32-msvc`

## 🔧 Solutions

### Solution 1: Force Install Windows Rollup Binaries

Run these commands to manually install the Windows-specific Rollup binaries:

```bash
# For Windows x64 (most common)
bun add --optional @rollup/rollup-win32-x64-msvc

# For Windows ARM64 (if using ARM-based Windows)
bun add --optional @rollup/rollup-win32-arm64-msvc

# For Windows 32-bit (if needed)
bun add --optional @rollup/rollup-win32-ia32-msvc
```

### Solution 2: Clean Install with Platform Detection

1. **Clear existing installation**:
```bash
rm -rf node_modules
rm bun.lock
```

2. **Reinstall with platform-specific flags**:
```bash
# Set environment variable for Windows
set BUN_INSTALL_OPTIONAL=true
bun install
```

### Solution 3: Use Rollup WASM Fallback

If native binaries continue to cause issues, you can force Rollup to use the WASM version:

1. **Install WASM version**:
```bash
bun add --dev @rollup/wasm-node
```

2. **Update vite.config.ts**:
```typescript
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  server: {
    port: 3000,
    hmr: { overlay: false },
  },
  build: {
    rollupOptions: {
      // Force use of WASM version on Windows
      ...(process.platform === 'win32' && {
        external: ['@rollup/rollup-win32-x64-msvc'],
      }),
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          router: ['react-router-dom'],
          ui: ['@headlessui/react'],
          utils: ['date-fns', 'zod'],
        },
      },
    },
    target: 'es2020',
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
      },
    },
  },
});
```

## 🚀 Recommended Windows Setup Process

### Step 1: Prerequisites
- Ensure you have Bun v1.2.18 installed
- Install Microsoft Visual C++ Redistributable (if not already installed)
- Use PowerShell or Command Prompt as Administrator

### Step 2: Clean Installation
```bash
# Navigate to project directory
cd SELA_Web

# Clean previous installation
rm -rf node_modules
rm bun.lock

# Install with Windows-specific configuration
set BUN_INSTALL_OPTIONAL=true
bun install

# Manually install Windows Rollup binaries
bun add --optional @rollup/rollup-win32-x64-msvc
```

### Step 3: Verify Installation
```bash
# Check if Windows binaries are installed
ls node_modules/@rollup/

# Test development server
bun dev
```

### Step 4: Test Build Process
```bash
# Test production build
bun run build

# Test preview
bun run preview
```

## 🔍 Troubleshooting

### If you still see blank page:

1. **Check browser console** for JavaScript errors
2. **Verify all dependencies** are properly installed:
   ```bash
   bun install --verbose
   ```
3. **Try development mode** first:
   ```bash
   bun dev
   ```
4. **Check network tab** in browser dev tools for failed requests

### If native.js is still missing:

1. **Use the WASM fallback** (Solution 3 above)
2. **Try npm as fallback**:
   ```bash
   npm install
   npm run dev
   ```

## 📝 Environment Variables

Create a `.env.local` file with Windows-specific settings:

```env
# Force WASM usage if needed
ROLLUP_USE_WASM=true

# Bun-specific settings
BUN_INSTALL_OPTIONAL=true
```

## 🆘 Emergency Fallback

If Bun continues to cause issues on Windows, you can temporarily use npm:

```bash
# Install with npm
npm install

# Run with npm
npm run dev
npm run build
```

Then gradually migrate back to Bun once the Windows compatibility issues are resolved.
