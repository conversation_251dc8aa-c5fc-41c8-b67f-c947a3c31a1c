import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Input } from '@/shared/components/Input';
import { Button } from '@/shared/components/Button';

const supportSchema = z.object({
  name: z.string().min(2, 'Tên phải có ít nhất 2 ký tự'),
  phone: z.string().min(10, 'Số điện thoại phải có ít nhất 10 số'),
  email: z.string().email('Email không hợp lệ').optional().or(z.literal('')),
  message: z.string().min(10, 'Nội dung hỗ trợ phải có ít nhất 10 ký tự'),
});

type SupportFormData = z.infer<typeof supportSchema>;

export function SupportForm() {
  const [isLoading, setIsLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<SupportFormData>({
    resolver: zodResolver(supportSchema),
  });

  const onSubmit = async (data: SupportFormData) => {
    setIsLoading(true);
    setError(null);

    try {
      // Simulate form submission - replace with actual API call
      console.log('Support form data:', data);
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setSuccess(true);
      reset();
      
      // Reset success message after 3 seconds
      setTimeout(() => {
        setSuccess(false);
      }, 3000);
    } catch (err) {
      setError('Có lỗi xảy ra khi gửi thông tin. Vui lòng thử lại.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h3 className="text-xl font-semibold text-gray-900 mb-6">
        Bạn Còn Cần Hỗ Trợ Thêm?
      </h3>
      
      {success && (
        <div className="mb-4 p-4 bg-green-50 border border-green-200 rounded-md">
          <p className="text-green-800">
            Cảm ơn bạn! Chúng tôi đã nhận được thông tin và sẽ liên hệ với bạn sớm nhất.
          </p>
        </div>
      )}

      {error && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
          <p className="text-red-800">{error}</p>
        </div>
      )}

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
            Tên *
          </label>
          <Input
            id="name"
            type="text"
            {...register('name')}
            placeholder="Nhập họ và tên của bạn"
            error={errors.name?.message}
          />
        </div>

        <div>
          <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
            Số Điện Thoại *
          </label>
          <Input
            id="phone"
            type="tel"
            {...register('phone')}
            placeholder="Nhập số điện thoại của bạn"
            error={errors.phone?.message}
          />
        </div>

        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
            Email (không bắt buộc)
          </label>
          <Input
            id="email"
            type="email"
            {...register('email')}
            placeholder="Nhập email của bạn"
            error={errors.email?.message}
          />
        </div>

        <div>
          <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
            Bạn cần hỗ trợ thông tin gì? *
          </label>
          <textarea
            id="message"
            {...register('message')}
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            placeholder="Mô tả chi tiết vấn đề bạn cần hỗ trợ..."
          />
          {errors.message && (
            <p className="mt-1 text-sm text-red-600">{errors.message.message}</p>
          )}
        </div>

        <Button
          type="submit"
          disabled={isLoading}
          className="w-full"
        >
          {isLoading ? 'Đang gửi...' : 'GỬI THÔNG TIN'}
        </Button>
      </form>
    </div>
  );
}
