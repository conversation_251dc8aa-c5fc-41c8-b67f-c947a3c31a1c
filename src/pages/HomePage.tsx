import { Link } from 'react-router-dom';
import { Button } from '@/shared/components/Button';
import { SupportForm } from '@/components/SupportForm';

export function HomePage() {
  return (
    <div className="bg-white">
      {/* Hero Section */}
      <div className="relative isolate px-6 pt-14 lg:px-8">
        <div className="mx-auto max-w-2xl py-32 sm:py-48 lg:py-56">
          <div className="text-center">
            <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl">
              Welcome to SELA_Web
            </h1>
            <p className="mt-6 text-lg leading-8 text-gray-600">
              Your modern educational platform for online learning. Discover courses, 
              track your progress, and achieve your learning goals.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Link to="/courses">
                <Button size="lg">
                  Browse Courses
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl lg:text-center">
            <h2 className="text-base font-semibold leading-7 text-primary-600">
              Learn Better
            </h2>
            <p className="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              Everything you need to succeed
            </p>
          </div>
          <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-4xl">
            <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-10 lg:max-w-none lg:grid-cols-2 lg:gap-y-16">
              <div className="relative pl-16">
                <dt className="text-base font-semibold leading-7 text-gray-900">
                  Video Learning
                </dt>
                <dd className="mt-2 text-base leading-7 text-gray-600">
                  High-quality video content with progress tracking and interactive features.
                </dd>
              </div>
              <div className="relative pl-16">
                <dt className="text-base font-semibold leading-7 text-gray-900">
                  Progress Tracking
                </dt>
                <dd className="mt-2 text-base leading-7 text-gray-600">
                  Monitor your learning journey with detailed analytics and milestones.
                </dd>
              </div>
              <div className="relative pl-16">
                <dt className="text-base font-semibold leading-7 text-gray-900">
                  Expert Instructors
                </dt>
                <dd className="mt-2 text-base leading-7 text-gray-600">
                  Learn from industry professionals and experienced educators.
                </dd>
              </div>
              <div className="relative pl-16">
                <dt className="text-base font-semibold leading-7 text-gray-900">
                  Flexible Learning
                </dt>
                <dd className="mt-2 text-base leading-7 text-gray-600">
                  Study at your own pace, anytime, anywhere with our responsive platform.
                </dd>
              </div>
            </dl>
          </div>
        </div>
      </div>

      {/* Support Form Section */}
      <div className="py-24 sm:py-32 bg-gray-50">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl">
            <SupportForm />
          </div>
        </div>
      </div>
    </div>
  );
}
