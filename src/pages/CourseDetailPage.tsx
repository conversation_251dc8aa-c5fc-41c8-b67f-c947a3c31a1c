import { useParams } from 'react-router-dom';
import { useCourse } from '@/features/courses/hooks/useCourses';

export function CourseDetailPage() {
  const { id } = useParams<{ id: string }>();
  const { data: course, isLoading, error } = useCourse(id!);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (error || !course) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center text-red-600">
          Course not found or error loading course.
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">{course.title}</h1>
        <p className="text-xl text-gray-600 mb-8">{course.description}</p>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            {course.video_url ? (
              <div className="aspect-video bg-gray-200 rounded-lg mb-6">
                {/* Video player will be implemented here */}
                <div className="flex items-center justify-center h-full text-gray-500">
                  Video Player (To be implemented)
                </div>
              </div>
            ) : (
              <div className="aspect-video bg-gray-200 rounded-lg mb-6 flex items-center justify-center">
                <span className="text-gray-500">No video available</span>
              </div>
            )}
            
            <div className="prose max-w-none">
              <h2>Course Details</h2>
              <p>{course.description}</p>
            </div>
          </div>
          
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-md p-6 sticky top-4">
              <div className="text-3xl font-bold text-primary-600 mb-4">
                ${course.price}
              </div>
              
              <div className="space-y-3 mb-6">
                <div className="flex justify-between">
                  <span className="text-gray-600">Level:</span>
                  <span className="capitalize">{course.level}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Category:</span>
                  <span>{course.category}</span>
                </div>
                {course.duration && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Duration:</span>
                    <span>{course.duration} minutes</span>
                  </div>
                )}
              </div>
              
              <button className="w-full bg-primary-600 text-white py-3 px-4 rounded-md hover:bg-primary-700 transition-colors">
                Enroll Now
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
