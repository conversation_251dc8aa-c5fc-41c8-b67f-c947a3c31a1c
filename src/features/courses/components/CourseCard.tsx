
import { Link } from 'react-router-dom';
import type { Course } from '@/types';
import { formatPrice, formatDuration } from '@/shared/utils/formatters';
import { VideoThumbnail } from '@/shared/components/VideoPlayer';
import { ProgressBar } from '@/features/progress/components/ProgressBar';
import { cn } from '@/shared/utils/cn';

interface CourseCardProps {
  course: Course;
  progress?: number;
  variant?: 'default' | 'compact' | 'detailed';
  className?: string;
  showProgress?: boolean;
}

export function CourseCard({
  course,
  progress,
  variant = 'default',
  className,
  showProgress = false,
}: CourseCardProps) {
  const baseClasses = 'bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-200';
  
  const variantClasses = {
    default: 'w-full',
    compact: 'w-64',
    detailed: 'w-full',
  };

  return (
    <Link
      to={`/courses/${course.id}`}
      className={cn(baseClasses, variantClasses[variant], className)}
    >
      <div className="relative">
        <VideoThumbnail
          thumbnailUrl={course.thumbnail_url}
          title={course.title}
          duration={course.duration}
          progress={progress}
          className="rounded-none"
        />
        
        {/* Course level badge */}
        <div className="absolute top-2 left-2">
          <span className={cn(
            'px-2 py-1 text-xs font-medium rounded-full',
            course.level === 'beginner' && 'bg-green-100 text-green-800',
            course.level === 'intermediate' && 'bg-yellow-100 text-yellow-800',
            course.level === 'advanced' && 'bg-red-100 text-red-800'
          )}>
            {course.level}
          </span>
        </div>
      </div>
      
      <div className="p-4">
        <div className="mb-2">
          <h3 className="text-lg font-semibold text-gray-900 line-clamp-2">
            {course.title}
          </h3>
          {course.instructor && course.instructor.full_name && (
            <p className="text-sm text-gray-600 mt-1">
              by {course.instructor.full_name}
            </p>
          )}
        </div>
        
        {variant === 'detailed' && (
          <p className="text-gray-600 text-sm mb-3 line-clamp-3">
            {course.description}
          </p>
        )}
        
        <div className="flex items-center justify-between mb-3">
          <span className="text-2xl font-bold text-primary-600">
            {formatPrice(course.price)}
          </span>
          {course.duration && (
            <span className="text-sm text-gray-500">
              {formatDuration(course.duration)}
            </span>
          )}
        </div>
        
        {course.category && (
          <div className="mb-3">
            <span className="inline-block bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full">
              {course.category}
            </span>
          </div>
        )}
        
        {showProgress && progress !== undefined && (
          <div className="mt-3">
            <ProgressBar
              progress={progress}
              size="sm"
              showLabel={false}
              className="mb-1"
            />
            <p className="text-xs text-gray-500">
              {progress >= 100 ? 'Completed' : `${Math.round(progress)}% complete`}
            </p>
          </div>
        )}
      </div>
    </Link>
  );
}

interface CourseGridProps {
  courses: Course[];
  progress?: Record<string, number>;
  variant?: 'default' | 'compact' | 'detailed';
  showProgress?: boolean;
  className?: string;
}

export function CourseGrid({
  courses,
  progress = {},
  variant = 'default',
  showProgress = false,
  className,
}: CourseGridProps) {
  const gridClasses = {
    default: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6',
    compact: 'grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4',
    detailed: 'grid grid-cols-1 lg:grid-cols-2 gap-8',
  };

  return (
    <div className={cn(gridClasses[variant], className)}>
      {courses.map((course) => (
        <CourseCard
          key={course.id}
          course={course}
          progress={progress[course.id]}
          variant={variant}
          showProgress={showProgress}
        />
      ))}
    </div>
  );
}
