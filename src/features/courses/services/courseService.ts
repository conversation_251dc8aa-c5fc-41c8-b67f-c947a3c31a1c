import { supabase } from '@/lib/supabase';
import type { Course } from '@/types';

// Type assertion function to convert database response to Course type
function assertCourse(data: any): Course {
  return data as Course;
}

function assertCourses(data: any[]): Course[] {
  return data as Course[];
}

export const courseService = {
  async getAll(): Promise<Course[]> {
    const { data, error } = await supabase
      .from('courses')
      .select(`
        *,
        instructor:profiles(id, full_name)
      `)
      .eq('is_published', true)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching courses:', error);
      throw new Error('Failed to fetch courses');
    }

    return assertCourses(data || []);
  },

  async getById(id: string): Promise<Course> {
    const { data, error } = await supabase
      .from('courses')
      .select(`
        *,
        instructor:profiles(id, full_name)
      `)
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching course:', error);
      throw new Error('Failed to fetch course');
    }

    return assertCourse(data);
  },

  async create(course: Omit<Course, 'id' | 'created_at' | 'updated_at'>): Promise<Course> {
    const { data, error } = await supabase
      .from('courses')
      .insert([course])
      .select()
      .single();

    if (error) {
      console.error('Error creating course:', error);
      throw new Error('Failed to create course');
    }

    return assertCourse(data);
  },

  async update(id: string, updates: Partial<Course>): Promise<Course> {
    const { data, error } = await supabase
      .from('courses')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating course:', error);
      throw new Error('Failed to update course');
    }

    return assertCourse(data);
  },

  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('courses')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting course:', error);
      throw new Error('Failed to delete course');
    }
  },

  async getByInstructor(instructorId: string): Promise<Course[]> {
    const { data, error } = await supabase
      .from('courses')
      .select('*')
      .eq('instructor_id', instructorId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching instructor courses:', error);
      throw new Error('Failed to fetch instructor courses');
    }

    return assertCourses(data || []);
  },
};
