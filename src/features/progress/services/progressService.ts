import { supabase } from '@/lib/supabase';
import type { UserProgress } from '@/types';

export const progressService = {
  async getUserProgress(userId: string, courseId: string): Promise<UserProgress | null> {
    const { data, error } = await supabase
      .from('user_progress')
      .select('*')
      .eq('user_id', userId)
      .eq('course_id', courseId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // No progress found, return null
        return null;
      }
      console.error('Error fetching user progress:', error);
      throw new Error('Failed to fetch user progress');
    }

    return data;
  },

  async updateProgress(
    userId: string,
    courseId: string,
    progressPercentage: number
  ): Promise<UserProgress> {
    const isCompleted = progressPercentage >= 100;
    const updateData: any = {
      user_id: userId,
      course_id: courseId,
      progress_percentage: Math.min(progressPercentage, 100),
      last_watched_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    if (isCompleted) {
      updateData.completed_at = new Date().toISOString();
    }

    const { data, error } = await supabase
      .from('user_progress')
      .upsert(updateData, {
        onConflict: 'user_id,course_id',
      })
      .select()
      .single();

    if (error) {
      console.error('Error updating progress:', error);
      throw new Error('Failed to update progress');
    }

    return data;
  },

  async getUserCourseProgress(userId: string): Promise<UserProgress[]> {
    const { data, error } = await supabase
      .from('user_progress')
      .select(`
        *,
        course:courses(id, title, thumbnail_url)
      `)
      .eq('user_id', userId)
      .order('updated_at', { ascending: false });

    if (error) {
      console.error('Error fetching user course progress:', error);
      throw new Error('Failed to fetch user course progress');
    }

    return data || [];
  },

  async getCompletedCourses(userId: string): Promise<UserProgress[]> {
    const { data, error } = await supabase
      .from('user_progress')
      .select(`
        *,
        course:courses(id, title, thumbnail_url)
      `)
      .eq('user_id', userId)
      .gte('progress_percentage', 100)
      .order('completed_at', { ascending: false });

    if (error) {
      console.error('Error fetching completed courses:', error);
      throw new Error('Failed to fetch completed courses');
    }

    return data || [];
  },

  async getCourseStats(courseId: string): Promise<{
    totalEnrolled: number;
    totalCompleted: number;
    averageProgress: number;
  }> {
    const { data, error } = await supabase
      .from('user_progress')
      .select('progress_percentage')
      .eq('course_id', courseId);

    if (error) {
      console.error('Error fetching course stats:', error);
      throw new Error('Failed to fetch course stats');
    }

    const totalEnrolled = data.length;
    const totalCompleted = data.filter(p => p.progress_percentage >= 100).length;
    const averageProgress = totalEnrolled > 0 
      ? data.reduce((sum, p) => sum + p.progress_percentage, 0) / totalEnrolled 
      : 0;

    return {
      totalEnrolled,
      totalCompleted,
      averageProgress: Math.round(averageProgress),
    };
  },
};
