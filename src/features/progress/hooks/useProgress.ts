import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

// Anonymous user ID for local storage
const ANONYMOUS_USER_ID = 'anonymous-user';

export function useUserProgress(courseId: string) {
  const queryClient = useQueryClient();

  // Use localStorage for anonymous progress tracking
  const { data: progress, isLoading, error } = useQuery({
    queryKey: ['progress', ANONYMOUS_USER_ID, courseId],
    queryFn: () => {
      const stored = localStorage.getItem(`progress-${courseId}`);
      return stored ? JSON.parse(stored) : { progress_percentage: 0 };
    },
    enabled: !!courseId,
  });

  const updateProgressMutation = useMutation({
    mutationFn: (progressPercentage: number) => {
      const progressData = { progress_percentage: progressPercentage };
      localStorage.setItem(`progress-${courseId}`, JSON.stringify(progressData));
      return Promise.resolve(progressData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['progress', ANONYMOUS_USER_ID, courseId] });
      queryClient.invalidateQueries({ queryKey: ['progress', ANONYMOUS_USER_ID] });
    },
  });

  return {
    progress,
    isLoading,
    error,
    updateProgress: updateProgressMutation.mutate,
    isUpdating: updateProgressMutation.isPending,
  };
}

export function useRealTimeProgress(courseId: string) {
  const [progress, setProgress] = useState<number>(0);
  const { progress: initialProgress, updateProgress } = useUserProgress(courseId);

  useEffect(() => {
    if (initialProgress) {
      setProgress(initialProgress.progress_percentage);
    }
  }, [initialProgress]);

  // No real-time subscription for anonymous users
  // Progress is stored locally and updated immediately

  const handleProgressUpdate = (newProgress: number) => {
    setProgress(newProgress);
    updateProgress(newProgress);
  };

  return {
    progress,
    updateProgress: handleProgressUpdate,
  };
}

export function useUserCourseProgress() {
  return useQuery({
    queryKey: ['progress', ANONYMOUS_USER_ID],
    queryFn: () => {
      // Get all progress from localStorage
      const allProgress = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key?.startsWith('progress-')) {
          const courseId = key.replace('progress-', '');
          const progress = JSON.parse(localStorage.getItem(key) || '{}');
          allProgress.push({ course_id: courseId, ...progress });
        }
      }
      return allProgress;
    },
  });
}

export function useCompletedCourses() {
  return useQuery({
    queryKey: ['progress', ANONYMOUS_USER_ID, 'completed'],
    queryFn: () => {
      // Get completed courses from localStorage
      const completed = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key?.startsWith('progress-')) {
          const courseId = key.replace('progress-', '');
          const progress = JSON.parse(localStorage.getItem(key) || '{}');
          if (progress.progress_percentage >= 100) {
            completed.push({ course_id: courseId, ...progress });
          }
        }
      }
      return completed;
    },
  });
}

export function useCourseStats(courseId: string) {
  return useQuery({
    queryKey: ['courseStats', courseId],
    queryFn: () => {
      // Return mock stats for anonymous users
      return {
        total_users: 0,
        completed_users: 0,
        average_progress: 0,
      };
    },
    enabled: !!courseId,
  });
}
