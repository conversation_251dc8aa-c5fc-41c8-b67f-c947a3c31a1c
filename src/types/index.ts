export interface User {
  id: string;
  email: string;
  full_name?: string | null;
  role: 'student' | 'instructor' | 'admin';
  created_at: string;
  updated_at: string;
}

export interface InstructorInfo {
  id: string;
  full_name: string | null;
}

export interface Course {
  id: string;
  title: string;
  description: string;
  price: number;
  instructor_id: string;
  instructor?: User | InstructorInfo;
  thumbnail_url?: string | null;
  video_url?: string | null;
  duration?: number | null; // in minutes
  level: 'beginner' | 'intermediate' | 'advanced';
  category: string;
  is_published: boolean;
  created_at: string;
  updated_at: string;
}

export interface UserProgress {
  id: string;
  user_id: string;
  course_id: string;
  progress_percentage: number;
  completed_at?: string | null;
  last_watched_at?: string | null;
  created_at: string;
  updated_at: string;
}

export interface Payment {
  id: string;
  user_id: string;
  course_id: string;
  amount: number;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  payment_method: string;
  transaction_id?: string;
  created_at: string;
  updated_at: string;
}

export interface ApiResponse<T> {
  data: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}
