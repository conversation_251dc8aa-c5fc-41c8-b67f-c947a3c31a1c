import { useCallback, useRef, useState } from 'react';
import ReactPlayer from 'react-player';
import { useRealTimeProgress } from '@/features/progress/hooks/useProgress';
import { cn } from '@/shared/utils/cn';

interface VideoPlayerProps {
  videoUrl: string;
  courseId: string;
  onProgress?: (progress: number) => void;
  className?: string;
  autoPlay?: boolean;
  controls?: boolean;
}

export function VideoPlayer({
  videoUrl,
  courseId,
  onProgress,
  className,
  autoPlay = false,
  controls = true,
}: VideoPlayerProps) {
  const { progress, updateProgress } = useRealTimeProgress(courseId);
  const [playing, setPlaying] = useState(autoPlay);
  const [duration, setDuration] = useState(0);
  const playerRef = useRef<ReactPlayer>(null);

  const handleProgress = useCallback(
    (progressData: { played: number; playedSeconds: number; loaded: number; loadedSeconds: number }) => {
      const progressPercentage = progressData.played * 100;
      
      // Update progress every 5% or when video ends
      if (progressPercentage > progress + 5 || progressPercentage >= 95) {
        updateProgress(progressPercentage);
      }
      
      onProgress?.(progressPercentage);
    },
    [progress, updateProgress, onProgress]
  );

  const handleDuration = useCallback((duration: number) => {
    setDuration(duration);
  }, []);

  const handleEnded = useCallback(() => {
    updateProgress(100);
    setPlaying(false);
  }, [updateProgress]);

  const handleSeek = useCallback((seconds: number) => {
    if (playerRef.current) {
      playerRef.current.seekTo(seconds);
    }
  }, []);

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // Remove user check - videos are now public

  return (
    <div className={cn('relative', className)}>
      <div className="aspect-video bg-black rounded-lg overflow-hidden">
        <ReactPlayer
          ref={playerRef}
          url={videoUrl}
          width="100%"
          height="100%"
          playing={playing}
          controls={controls}
          onProgress={handleProgress}
          onDuration={handleDuration}
          onEnded={handleEnded}
          onPlay={() => setPlaying(true)}
          onPause={() => setPlaying(false)}
          progressInterval={1000} // Update progress every second
          config={{
            file: {
              attributes: {
                preload: 'metadata',
                controlsList: 'nodownload',
              },
            },
            youtube: {
              playerVars: {
                showinfo: 0,
                modestbranding: 1,
              },
            },
          }}
        />
      </div>
      
      {/* Progress indicator */}
      {progress > 0 && (
        <div className="mt-2 flex items-center justify-between text-sm text-gray-600">
          <span>Progress: {Math.round(progress)}%</span>
          {duration > 0 && (
            <span>Duration: {formatTime(duration)}</span>
          )}
        </div>
      )}
      
      {/* Resume from last position */}
      {progress > 5 && progress < 95 && (
        <div className="mt-2">
          <button
            onClick={() => handleSeek((progress / 100) * duration)}
            className="text-sm text-primary-600 hover:text-primary-700 underline"
          >
            Resume from {Math.round(progress)}%
          </button>
        </div>
      )}
    </div>
  );
}

interface VideoThumbnailProps {
  thumbnailUrl?: string | null;
  title: string;
  duration?: number | null;
  progress?: number;
  onClick?: () => void;
  className?: string;
}

export function VideoThumbnail({
  thumbnailUrl,
  title,
  duration,
  progress = 0,
  onClick,
  className,
}: VideoThumbnailProps) {
  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  return (
    <div
      className={cn(
        'relative aspect-video bg-gray-200 rounded-lg overflow-hidden cursor-pointer group',
        className
      )}
      onClick={onClick}
    >
      {thumbnailUrl ? (
        <img
          src={thumbnailUrl}
          alt={title}
          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
        />
      ) : (
        <div className="w-full h-full flex items-center justify-center bg-gray-300">
          <span className="text-gray-500">No thumbnail</span>
        </div>
      )}
      
      {/* Play button overlay */}
      <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
        <div className="w-16 h-16 bg-white bg-opacity-90 rounded-full flex items-center justify-center">
          <svg className="w-6 h-6 text-gray-800 ml-1" fill="currentColor" viewBox="0 0 20 20">
            <path d="M8 5v10l8-5-8-5z" />
          </svg>
        </div>
      </div>
      
      {/* Duration badge */}
      {duration && (
        <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
          {formatDuration(duration)}
        </div>
      )}
      
      {/* Progress bar */}
      {progress > 0 && (
        <div className="absolute bottom-0 left-0 right-0 h-1 bg-gray-600 bg-opacity-50">
          <div
            className="h-full bg-primary-600 transition-all duration-300"
            style={{ width: `${progress}%` }}
          />
        </div>
      )}
    </div>
  );
}
