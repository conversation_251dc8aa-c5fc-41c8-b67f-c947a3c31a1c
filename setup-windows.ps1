# Windows Setup Script for SELA_Web with Bun v1.2.18
# Run this script in PowerShell as Administrator

Write-Host "🚀 Setting up SELA_Web for Windows..." -ForegroundColor Green

# Check if Bun is installed
Write-Host "📦 Checking Bun installation..." -ForegroundColor Yellow
try {
    $bunVersion = bun --version
    Write-Host "✅ Bun version: $bunVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Bun is not installed. Please install Bun first: https://bun.sh" -ForegroundColor Red
    exit 1
}

# Set environment variables for Windows compatibility
Write-Host "🔧 Setting Windows-specific environment variables..." -ForegroundColor Yellow
$env:BUN_INSTALL_OPTIONAL = "true"
$env:ROLLUP_USE_WASM = "false"

# Clean previous installation
Write-Host "🧹 Cleaning previous installation..." -ForegroundColor Yellow
if (Test-Path "node_modules") {
    Remove-Item -Recurse -Force "node_modules"
    Write-Host "✅ Removed node_modules" -ForegroundColor Green
}

if (Test-Path "bun.lock") {
    Remove-Item -Force "bun.lock"
    Write-Host "✅ Removed bun.lock" -ForegroundColor Green
}

# Install dependencies
Write-Host "📦 Installing dependencies with Bun..." -ForegroundColor Yellow
try {
    bun install
    Write-Host "✅ Dependencies installed successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to install dependencies with Bun" -ForegroundColor Red
    Write-Host "🔄 Trying with npm as fallback..." -ForegroundColor Yellow
    npm install
}

# Detect Windows architecture and install appropriate Rollup binaries
Write-Host "🏗️ Installing Windows-specific Rollup binaries..." -ForegroundColor Yellow

$arch = $env:PROCESSOR_ARCHITECTURE
Write-Host "Detected architecture: $arch" -ForegroundColor Cyan

switch ($arch) {
    "AMD64" {
        Write-Host "Installing x64 binaries..." -ForegroundColor Cyan
        bun add --optional "@rollup/rollup-win32-x64-msvc"
    }
    "ARM64" {
        Write-Host "Installing ARM64 binaries..." -ForegroundColor Cyan
        bun add --optional "@rollup/rollup-win32-arm64-msvc"
    }
    "x86" {
        Write-Host "Installing x86 binaries..." -ForegroundColor Cyan
        bun add --optional "@rollup/rollup-win32-ia32-msvc"
    }
    default {
        Write-Host "Unknown architecture, installing x64 binaries as default..." -ForegroundColor Yellow
        bun add --optional "@rollup/rollup-win32-x64-msvc"
    }
}

# Verify installation
Write-Host "🔍 Verifying installation..." -ForegroundColor Yellow

# Check if Rollup Windows binaries are installed
$rollupPath = "node_modules/@rollup"
if (Test-Path $rollupPath) {
    $rollupDirs = Get-ChildItem $rollupPath -Directory | Where-Object { $_.Name -like "*win32*" }
    if ($rollupDirs.Count -gt 0) {
        Write-Host "✅ Windows Rollup binaries found:" -ForegroundColor Green
        foreach ($dir in $rollupDirs) {
            Write-Host "  - $($dir.Name)" -ForegroundColor Cyan
        }
    } else {
        Write-Host "⚠️ No Windows Rollup binaries found. Using WASM fallback." -ForegroundColor Yellow
        $env:ROLLUP_USE_WASM = "true"
    }
} else {
    Write-Host "❌ Rollup not found in node_modules" -ForegroundColor Red
}

# Test development server
Write-Host "🧪 Testing development server..." -ForegroundColor Yellow
Write-Host "Starting development server for 5 seconds to test..." -ForegroundColor Cyan

$devProcess = Start-Process -FilePath "bun" -ArgumentList "dev" -PassThru -NoNewWindow
Start-Sleep -Seconds 5

if (!$devProcess.HasExited) {
    Write-Host "✅ Development server started successfully!" -ForegroundColor Green
    Write-Host "🌐 Server should be running at http://localhost:3000" -ForegroundColor Cyan
    
    # Stop the test server
    Stop-Process -Id $devProcess.Id -Force
    Write-Host "🛑 Test server stopped" -ForegroundColor Yellow
} else {
    Write-Host "❌ Development server failed to start" -ForegroundColor Red
    Write-Host "📋 Check the logs above for errors" -ForegroundColor Yellow
}

# Test build process
Write-Host "🏗️ Testing build process..." -ForegroundColor Yellow
try {
    bun run build
    Write-Host "✅ Build completed successfully!" -ForegroundColor Green
} catch {
    Write-Host "❌ Build failed. Check the error messages above." -ForegroundColor Red
    Write-Host "💡 Try running with WASM fallback:" -ForegroundColor Yellow
    Write-Host "   $env:ROLLUP_USE_WASM = 'true'; bun run build" -ForegroundColor Cyan
}

# Final instructions
Write-Host ""
Write-Host "🎉 Setup completed!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Next steps:" -ForegroundColor Yellow
Write-Host "1. Run 'bun dev' to start the development server" -ForegroundColor Cyan
Write-Host "2. Open http://localhost:3000 in your browser" -ForegroundColor Cyan
Write-Host "3. If you encounter issues, check WINDOWS_SETUP.md" -ForegroundColor Cyan
Write-Host ""
Write-Host "🆘 If problems persist:" -ForegroundColor Yellow
Write-Host "- Use 'npm install && npm run dev' as fallback" -ForegroundColor Cyan
Write-Host "- Check browser console for JavaScript errors" -ForegroundColor Cyan
Write-Host "- Ensure Microsoft Visual C++ Redistributable is installed" -ForegroundColor Cyan
Write-Host ""
Write-Host "Happy coding! 🚀" -ForegroundColor Green
